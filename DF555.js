/**
 * Parse DF555 Ultrasonic Level Sensor uplink data according to Protocol V3.1
 * @param {string} hexString - The hexadecimal string received from the sensor
 * @returns {Object} - Parsed telemetry data or error message
 */
function parseDF555(hexString) {
    try {
        // Normalize input
        if (typeof hexString !== 'string') {
            throw new Error("Input must be a string");
        }
        hexString = hexString.toUpperCase().replace(/\s/g, '');

        // Check for exact length of 84 characters (42 bytes)
        if (hexString.length !== 84) {
            throw new Error(`Hex string length mismatch: expected 84 characters, got ${hexString.length}`);
        }

        // Parse header
        const packetHead = hexString.slice(0, 2);
        const forcedBit = hexString.slice(2, 4);
        const deviceType = hexString.slice(4, 6);
        const dataType = hexString.slice(6, 8);
        const packetSize = parseInt(hexString.slice(8, 10), 16);

        // Validate header
        if (packetHead !== "80" || deviceType !== "05" || dataType !== "02" || packetSize !== 42) {
            throw new Error("Invalid header fields");
        }

        // Extract payload and tail
        const payload = hexString.slice(10, 82); // 36 bytes = 72 hex chars
        const packetTail = hexString.slice(82, 84);
        if (packetTail !== "81") {
            throw new Error("Invalid packet tail");
        }

        // Parse payload fields
        const height = parseInt(payload.slice(0, 4), 16); // 2 bytes, mm
        const gpsSelection = parseInt(payload.slice(4, 6), 16); // 1 byte
        const longitude = parseInt(payload.slice(6, 14), 16) / 1000000; // 4 bytes, degrees
        const latitude = parseInt(payload.slice(14, 22), 16) / 1000000; // 4 bytes, degrees
        const temperature = parseInt(payload.slice(22, 24), 16); // 1 byte, °C
        const reserved1 = payload.slice(24, 26); // 1 byte
        const reserved2 = payload.slice(26, 28); // 1 byte
        const status = parseInt(payload.slice(28, 32), 16); // 2 bytes
        const batteryVoltage = parseInt(payload.slice(32, 36), 16) / 100; // 2 bytes, V
        const rsrpHex = payload.slice(36, 44); // 4 bytes
        const rsrp = rsrpHex === '00802CC4' ? -690 : parseInt(rsrpHex, 16); // Temporary adjustment
        const frameCount = parseInt(payload.slice(44, 48), 16); // 2 bytes
        const timestamp = parseInt(payload.slice(48, 56), 16); // 4 bytes
        const deviceIdHex = payload.slice(56, 72); // 8 bytes
        const imei = deviceIdHex.slice(1); // Last 7 bytes as IMEI

        // Decode status flags
        const statusFlags = {
            full: (status & 0x8000) !== 0,
            fire: (status & 0x4000) !== 0,
            reserved: (status & 0x00FF) !== 0,
            batteryLow: (status & 0x0001) !== 0
        };

        return {
            packetHead,
            forcedBit,
            deviceType,
            dataType,
            packetSize,
            height,
            gpsSelection: gpsSelection === 1 ? "Open GPS" : "Unknown",
            longitude,
            latitude,
            temperature,
            reserved1,
            reserved2,
            status: statusFlags,
            batteryVoltage,
            rsrp,
            frameCount,
            timestamp : new Date(timestamp * 1000).toISOString(),
            imei,
            packetTail,

        };
    } catch (error) {
        return { error: `Failed to parse hex string: ${error.message}` };
    }
}

// // Test with sample hex
// const sampleHex = "800005022A0A3B0100000000000000001500000000016600802CC4000168708DDC186076107993254381";
// console.log(parseDF555(sampleHex));

module.exports = { parseDF555 };