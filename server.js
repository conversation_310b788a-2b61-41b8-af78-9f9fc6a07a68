require('dotenv').config();
const net = require('net');
const axios = require('axios');
const { parseDF555 } = require('./DF555.js');

const PORT = process.env.TCP_PORT || 4000;
const API_ENDPOINT = process.env.API_ENDPOINT || 'http://localhost:3000/webhook';

// Create TCP server
const server = net.createServer((socket) => {
    console.log('Client connected');

    socket.on('data', async (data) => {
        try {
            console.log('Received data:', data);
            console.log('Received raw data:', data.toString('hex'));

            // Convert Buffer to hex string for DF555 parser
            const payload = data.toString('hex');

            // Parse the data using DF555 parser
            const parsedData = parseDF555(payload);
            
            if (parsedData && parsedData.deviceType === 'DF555') {
                // Log the raw data
                console.log('Parsed data:', parsedData);

            } else {
                console.log('Unknown or invalid data:', parsedData);
                socket.write('Invalid data received\n');
            }
        } catch (error) {
            console.error('Error processing data:', error.message);
            socket.write(`Error: ${error.message}\n`);
        }
    });

    socket.on('end', () => {
        console.log('Client disconnected');
    });

    socket.on('error', (err) => {
        console.error('Socket error:', err.message);
    });
});

server.on('error', (err) => {
    console.error('Server error:', err.message);
});

// Start server
server.listen(PORT, () => {
    console.log(`TCP Server listening on port ${PORT}`);
});