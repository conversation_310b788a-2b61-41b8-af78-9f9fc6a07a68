# =============================================================================
# WATABOT TCP SERVER ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and configure the values according to your setup
# =============================================================================

# -----------------------------------------------------------------------------
# SERVER CONFIGURATION
# -----------------------------------------------------------------------------

# TCP Server Port
# The port on which the TCP server will listen for incoming connections
# Default: 4000
TCP_PORT=4000

# Server Host (optional)
# The host interface to bind the server to
# Default: 0.0.0.0 (all interfaces)
# TCP_HOST=0.0.0.0

# -----------------------------------------------------------------------------
# API CONFIGURATION
# -----------------------------------------------------------------------------

# API Endpoint
# The webhook URL where parsed sensor data will be sent
# This should be your backend API endpoint that handles DF555 sensor data
API_ENDPOINT=https://your-api-domain.com/webhook/df555

# API Timeout (optional)
# Timeout for API requests in milliseconds
# Default: 5000 (5 seconds)
# API_TIMEOUT=5000

# API Retry Attempts (optional)
# Number of retry attempts for failed API calls
# Default: 3
# API_RETRY_ATTEMPTS=3

# -----------------------------------------------------------------------------
# LOGGING CONFIGURATION
# -----------------------------------------------------------------------------

# Log Level
# Controls the verbosity of logging
# Options: error, warn, info, debug
# Default: info
LOG_LEVEL=info

# Enable Raw Data Logging
# Whether to log raw hex data received from sensors
# Options: true, false
# Default: true
LOG_RAW_DATA=true

# Enable Parsed Data Logging
# Whether to log parsed sensor data
# Options: true, false
# Default: true
LOG_PARSED_DATA=true

# -----------------------------------------------------------------------------
# SECURITY CONFIGURATION
# -----------------------------------------------------------------------------

# API Key (optional)
# API key for authenticating with your webhook endpoint
# API_KEY=your-secret-api-key

# Allowed Client IPs (optional)
# Comma-separated list of IP addresses allowed to connect
# Leave empty to allow all connections
# ALLOWED_IPS=*************,*********

# -----------------------------------------------------------------------------
# SENSOR CONFIGURATION
# -----------------------------------------------------------------------------

# Device Validation
# Whether to validate device type in incoming data
# Options: true, false
# Default: true
VALIDATE_DEVICE_TYPE=true

# Expected Device Type
# The expected device type code for DF555 sensors
# Default: 05
EXPECTED_DEVICE_TYPE=05

# Data Type Validation
# Whether to validate data type in incoming packets
# Options: true, false
# Default: true
VALIDATE_DATA_TYPE=true

# Expected Data Type
# The expected data type code for telemetry data
# Default: 02
EXPECTED_DATA_TYPE=02

# -----------------------------------------------------------------------------
# DEVELOPMENT CONFIGURATION
# -----------------------------------------------------------------------------

# Node Environment
# Current environment (development, production, test)
# Default: development
NODE_ENV=development

# Debug Mode
# Enable debug mode for additional logging
# Options: true, false
# Default: false
DEBUG=false

# Mock Mode (optional)
# Enable mock mode for testing without real sensor data
# Options: true, false
# Default: false
# MOCK_MODE=false

# -----------------------------------------------------------------------------
# MONITORING CONFIGURATION
# -----------------------------------------------------------------------------

# Health Check Port (optional)
# Port for health check endpoint
# HEALTH_CHECK_PORT=4001

# Metrics Collection (optional)
# Enable metrics collection
# Options: true, false
# Default: false
# ENABLE_METRICS=false

# -----------------------------------------------------------------------------
# DATABASE CONFIGURATION (if needed in future)
# -----------------------------------------------------------------------------

# Database URL (optional)
# Connection string for database if data persistence is added
# DATABASE_URL=postgresql://username:password@localhost:5432/watabot

# Redis URL (optional)
# Connection string for Redis if caching is added
# REDIS_URL=redis://localhost:6379
